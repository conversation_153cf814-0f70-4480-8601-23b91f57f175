import { type HTMLAttributes, useId } from "react";
import RsdsButton from "../../rsds-button/RsdsButton";
import RsdsLogo from "../../rsds-logo/RsdsLogo";

export interface PickRoleProps extends HTMLAttributes<HTMLDivElement> {}

/**
 * Primary UI component for user interaction
 */
export const PickRole = (props: PickRoleProps) => {
	const id = useId();

	return (
		<div {...props} className="w-full">
			<div className="mx-auto max-w-lg border rounded-2xl p-9 flex flex-col gap-y-9">
				<div className="flex flex-col items-center gap-y-6">
					<RsdsLogo />
					<h1>Labsvar i Skåne</h1>
				</div>
				<fieldset className="flex flex-col">
					<legend>Välj ditt aktuella medarbetaruppdrag:</legend>
					<div className="flex gap-x-3 py-3">
						<input name="PractionerRole" id={id} type="radio" />
						<label htmlFor={id} className="">
							<div className="font-bold">VoB Internmedicinsk vård Ystad</div>
							<div>Läkare</div>
						</label>
					</div>
					<div className="flex gap-x-3 py-3">
						<input name="PractionerRole" id={id} type="radio" />
						<label htmlFor={id}>
							<div className="font-bold">VoB Internmedicinsk vård Ystad</div>
							<div>Läkare</div>
						</label>
					</div>
				</fieldset>

				<RsdsButton className="self-center" variant="main">
					Öppna labsvar i Skåne
				</RsdsButton>
			</div>
		</div>
	);
};

export default PickRole;
