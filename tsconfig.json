{
	"compilerOptions": {
		"target": "es2017",
		"lib": ["dom", "dom.iterable", "esnext"],
		"allowJs": true,
		"skipLibCheck": true,
		"strict": true,
		"forceConsistentCasingInFileNames": true,
		"noEmit": true,
		"esModuleInterop": true,
		"module": "esnext",
		"moduleResolution": "node",
		"resolveJsonModule": true,
		"isolatedModules": true,
		"jsx": "react-jsx",

		"declaration": true,
		"types": ["vitest/globals", "@testing-library/jest-dom"]
	},
	"include": ["src", "src/type.d.ts"],
	"exclude": ["node_modules", "dist", "**/*.test.tsx"]
	// "exclude": ["node_modules", "dist", "**/*.stories.tsx", "**/*.test.tsx"]
}
