import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { LabTableRowHeader, type LabTableRowHeaderProps } from "./LabTableRowHeader";

const meta: Meta<typeof LabTableRowHeader> = {
	title: "UI/TableRowHeader",
	component: LabTableRowHeader,
	parameters: {
		layout: "centered",
	},
	tags: ["autodocs"],
	argTypes: {
		textAlign: {
			control: "select",
			options: ["left", "center", "right"],
		},
		children: {
			control: "text",
		},
	},
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
	args: {
		children: "Hematologi, Inflammation",
	},
	render: (args: LabTableRowHeaderProps) => (
		<table className="border-collapse">
			<thead>
				<tr>
					<LabTableRowHeader {...args} />
				</tr>
			</thead>
		</table>
	),
};

export const CenterAligned: Story = {
	args: {
		children: "Centered Header",
		textAlign: "center",
	},
	render: (args: LabTableRowHeaderProps) => (
		<table className="border-collapse">
			<thead>
				<tr>
					<LabTableRowHeader {...args} />
				</tr>
			</thead>
		</table>
	),
};

export const RightAligned: Story = {
	args: {
		children: "Right Header",
		textAlign: "right",
	},
	render: (args: LabTableRowHeaderProps) => (
		<table className="border-collapse">
			<thead>
				<tr>
					<LabTableRowHeader {...args} />
				</tr>
			</thead>
		</table>
	),
};

export const MultipleHeaders: Story = {
	render: () => (
		<table className="border-collapse">
			<thead>
				<tr>
					<LabTableRowHeader>Hematologi, Inflammation</LabTableRowHeader>
					<LabTableRowHeader>Mikrobiologi</LabTableRowHeader>
					<LabTableRowHeader textAlign="center">Radiologi</LabTableRowHeader>
					<LabTableRowHeader textAlign="right">Patologi</LabTableRowHeader>
				</tr>
			</thead>
		</table>
	),
};
