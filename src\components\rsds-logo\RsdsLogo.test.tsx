import { render, screen } from "@testing-library/react";
import { describe, expect, it } from "vitest";
import RsdsLogo from "./RsdsLogo";

describe("RsdsLogo", () => {
	it("renders correctly with default props", () => {
		render(<RsdsLogo />);

		const logo = screen.getByRole("img", { name: "RS Design System Logo" });
		expect(logo).toBeDefined();
	});

	it("renders with custom alt text", () => {
		render(<RsdsLogo alt="Custom logo text" />);

		const logo = screen.getByRole("img", { name: "Custom logo text" });
		expect(logo).toBeDefined();
	});

	it("applies correct variant and size", () => {
		render(<RsdsLogo variant="black-gray" size="lg" />);

		const logo = screen.getByRole("img");
		expect(logo).toBeDefined();
		expect(logo.getAttribute("aria-label")).toBe("RS Design System Logo");
	});

	it("applies custom className", () => {
		render(<RsdsLogo className="custom-class" />);

		const logo = screen.getByRole("img");
		expect(logo).toBeDefined();

		// For SVG elements, we need to use getAttribute('class') instead of className
		// because className returns an SVGAnimatedString object
		const classAttribute = logo.getAttribute("class");
		expect(classAttribute).toBeTruthy();
		expect(classAttribute).toContain("custom-class");

		// Also verify that default classes are applied
		expect(classAttribute).toContain("inline-block");
		expect(classAttribute).toContain("h-[60px]");
		expect(classAttribute).toContain("w-16");
	});

	it("uses Tailwind CSS custom properties for colors", () => {
		render(<RsdsLogo variant="color" />);

		const logo = screen.getByRole("img");
		expect(logo).toBeDefined();

		// Check that SVG paths exist (indicating the component rendered properly)
		const paths = logo.querySelectorAll("path");
		expect(paths.length).toBeGreaterThan(0);

		// Verify that the SVG has the expected structure with fill attributes
		// The actual color values will be CSS custom properties
		const pathsWithFill = Array.from(paths).filter((path) =>
			path.getAttribute("fill"),
		);
		expect(pathsWithFill.length).toBeGreaterThan(0);
	});
});
