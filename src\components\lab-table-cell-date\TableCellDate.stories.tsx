import type { <PERSON>a, StoryObj } from "@storybook/react";
import TableCellDate from "./TableCellDate";

const meta: Meta<typeof TableCellDate> = {
	title: "UI/TableCellDate",
	component: TableCellDate,
	parameters: {
		layout: "centered",
	},
	tags: ["autodocs"],
	argTypes: {
		dateTime: {
			control: "text",
			description: "The date and time in the format 'YYYY-MM-DD HH:mm'",
		},
		type: {
			control: "select",
			options: ["th", "td"],
			description: "Type of table cell",
		},
		textAlign: {
			control: "select",
			options: ["left", "center", "right"],
			description: "Text alignment within the cell",
		},
		textStyle: {
			control: "select",
			options: ["sans"],
			description: "Text style variant",
		},
	},
};

export default meta;
type Story = StoryObj<typeof meta>;

export const HeaderCell: Story = {
	args: {
		dateTime: "2025-03-01 12:33",
		type: "th",
		textAlign: "left",
		textStyle: "sans",
	},
};

export const DataCell: Story = {
	args: {
		dateTime: "2025-03-01 12:33",
		type: "td",
		textAlign: "left",
		textStyle: "sans",
	},
};

export const TextAlignments: Story = {
	render: () => (
		<table className="border-collapse">
			<thead>
				<tr>
					<TableCellDate
						type="th"
						textAlign="left"
						dateTime="2025-03-01 12:33"
					/>
					<TableCellDate
						type="th"
						textAlign="center"
						dateTime="2025-03-01 12:33"
					/>
					<TableCellDate
						type="th"
						textAlign="right"
						dateTime="2025-03-01 12:33"
					/>
				</tr>
			</thead>
			<tbody>
				<tr>
					<TableCellDate
						type="td"
						textAlign="left"
						dateTime="2025-03-01 12:33"
					/>
					<TableCellDate
						type="td"
						textAlign="center"
						dateTime="2025-03-01 12:33"
					/>
					<TableCellDate
						type="td"
						textAlign="right"
						dateTime="2025-03-01 12:33"
					/>
				</tr>
			</tbody>
		</table>
	),
};

export const CompleteTable: Story = {
	render: () => (
		<div className="w-full max-w-4xl">
			<table className="w-full border-collapse">
				<thead>
					<tr>
						<TableCellDate
							type="th"
							textAlign="left"
							dateTime="2025-03-01 12:33"
						/>
						<TableCellDate
							type="th"
							textAlign="left"
							dateTime="2025-03-02 14:45"
						/>
						<TableCellDate
							type="th"
							textAlign="center"
							dateTime="2025-03-03 09:15"
						/>
						<TableCellDate
							type="th"
							textAlign="right"
							dateTime="2025-03-04 16:20"
						/>
					</tr>
				</thead>
				<tbody>
					<tr>
						<TableCellDate
							type="td"
							textAlign="left"
							dateTime="2025-03-05 08:30"
						/>
						<TableCellDate
							type="td"
							textAlign="left"
							dateTime="2025-03-06 11:45"
						/>
						<TableCellDate
							type="td"
							textAlign="center"
							dateTime="2025-03-07 13:20"
						/>
						<TableCellDate
							type="td"
							textAlign="right"
							dateTime="2025-03-08 17:10"
						/>
					</tr>
					<tr>
						<TableCellDate
							type="td"
							textAlign="left"
							dateTime="2025-03-09 07:00"
						/>
						<TableCellDate
							type="td"
							textAlign="left"
							dateTime="2025-03-10 10:30"
						/>
						<TableCellDate
							type="td"
							textAlign="center"
							dateTime="2025-03-11 15:45"
						/>
						<TableCellDate
							type="td"
							textAlign="right"
							dateTime="2025-03-12 19:25"
						/>
					</tr>
				</tbody>
			</table>
		</div>
	),
};
